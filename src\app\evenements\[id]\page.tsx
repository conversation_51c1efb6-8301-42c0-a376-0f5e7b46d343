// app/evenements/[id]/page.tsx
"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import {
  MapPin,
  Calendar,
  Users,
  Mic,
  Mail,
  Globe,
  ArrowLeft,
  User,
} from "lucide-react";
import Link from "next/link";
import { Footer } from "@/components/layout/Footer";
import { Header } from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { mockEvenements } from "@/data/mockData";
import { useFavorites } from "@/hooks/useLocalStorage";
import { formatDate } from "@/lib/utils";

export default function EvenementDetailPage() {
  const [isMounted, setIsMounted] = useState(false);
  const params = useParams();
  const eventId = params.id as string;
  const { value: favorites, setValue: setFavorites } = useFavorites();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Trouver l'événement correspondant
  const event = mockEvenements.find((e) => e.id === eventId);

  const handleFavoriteToggle = () => {
    if (!eventId) return;
    const newFavorites = favorites.includes(eventId)
      ? favorites.filter((fav) => fav !== eventId)
      : [...favorites, eventId];
    setFavorites(newFavorites);
  };

  const handleShare = async () => {
    if (!event) return;
    try {
      if (navigator.share) {
        await navigator.share({
          title: event.title,
          text: event.description,
          url: window.location.href,
        });
      } else {
        await navigator.clipboard.writeText(window.location.href);
        alert("Lien copié dans le presse-papier !");
      }
    } catch (error) {
      console.error("Erreur lors du partage :", error);
    }
  };

  if (!isMounted) {
    return (
      <main>
        <Header />
        <div className="container mx-auto px-4 py-16">
          <div className="h-6 w-40 bg-gray-200 rounded mb-6 animate-pulse" />
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <div className="h-10 w-64 bg-gray-200 rounded mb-4 animate-pulse" />
              <div className="h-4 w-full bg-gray-200 rounded mb-2 animate-pulse" />
              <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="space-y-6">
              <div className="h-40 bg-gray-200 rounded animate-pulse" />
              <div className="h-40 bg-gray-200 rounded animate-pulse" />
              <div className="h-40 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  if (!event) {
    return (
      <main>
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">Événement non trouvé</h1>
          <p className="text-muted-foreground mb-8">
            L'événement que vous recherchez n'existe pas ou a été supprimé.
          </p>
          <Link href="/evenements">
            <Button>Retour aux événements</Button>
          </Link>
        </div>
        <Footer />
      </main>
    );
  }

  return (
    <main>
      <Header />
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link
            href="/evenements"
            className="flex items-center text-muted-foreground hover:text-primary cursor-pointer"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour aux événements
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Contenu principal */}
          <div className="lg:col-span-2 space-y-8">
            {/* En-tête */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <Badge variant="secondary" className="mb-4">
                  {event.type.charAt(0).toUpperCase() + event.type.slice(1)}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                {event.title}
              </h1>

              <div className="flex items-center gap-4 text-muted-foreground mb-6">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>{event.organizer}</span>
                </div>
                {!event.isVirtual && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>
                      {event.location.city}, {event.location.region}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {event.description}
                </p>
              </CardContent>
            </Card>

            {/* Sujets */}
            {event.topics && event.topics.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Sujets abordés</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {event.topics.map((topic, index) => (
                      <Badge key={index} variant="secondary">
                        {topic}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Conférenciers */}
            {event.speakers && event.speakers.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Conférenciers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {event.speakers.map((speaker, index) => (
                      <div key={index} className="flex items-start gap-4">
                        <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
                        <div>
                          <div className="font-medium">{speaker.name}</div>
                          <div className="text-muted-foreground">
                            {speaker.title}
                            {speaker.company && `, ${speaker.company}`}
                          </div>
                          {speaker.bio && (
                            <p className="text-sm text-muted-foreground mt-2">
                              {speaker.bio}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Informations pratiques */}
            <Card>
              <CardHeader>
                <CardTitle>Informations pratiques</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Date de début</div>
                        <div className="text-muted-foreground">
                          {formatDate(event.startDate)}
                        </div>
                      </div>
                    </div>

                    {event.endDate && (
                      <div className="flex items-center gap-3">
                        <Calendar className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">Date de fin</div>
                          <div className="text-muted-foreground">
                            {formatDate(event.endDate)}
                          </div>
                        </div>
                      </div>
                    )}

                    {event.registrationDeadline && (
                      <div className="flex items-center gap-3">
                        <Calendar className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">
                            Date limite d'inscription
                          </div>
                          <div className="text-muted-foreground">
                            {formatDate(event.registrationDeadline)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Public cible</div>
                        <div className="text-muted-foreground">
                          {event.targetAudience.join(", ")}
                        </div>
                      </div>
                    </div>

                    {event.capacity && (
                      <div className="flex items-center gap-3">
                        <Users className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">Capacité</div>
                          <div className="text-muted-foreground">
                            {event.capacity} places
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Format</div>
                        <div className="text-muted-foreground">
                          {event.isVirtual ? "Virtuel" : "Présentiel"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Actions */}
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Link
                    href={event.registrationUrl || "#"}
                    className="cursor-pointer"
                  >
                    <Button className="w-full" size="lg">
                      {event.isFree ? "S'inscrire gratuitement" : "S'inscrire"}
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleFavoriteToggle}
                  >
                    {favorites.includes(eventId)
                      ? "Retirer des favoris"
                      : "Ajouter aux favoris"}
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleShare}
                  >
                    Partager
                  </Button>
                </div>

                {!event.isFree && event.price !== undefined && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="text-sm font-medium text-blue-800">
                      Tarif
                    </div>
                    <div className="text-sm text-blue-600">{event.price} €</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Organisateur</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="font-medium mb-2">{event.organizer}</div>
                  {!event.isVirtual && (
                    <div className="text-sm text-muted-foreground">
                      {event.location.address}
                      <br />
                      {event.location.postalCode} {event.location.city}
                    </div>
                  )}
                </div>

                {event.contactEmail && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={`mailto:${event.contactEmail}`}
                      className="text-sm text-primary hover:underline cursor-pointer"
                    >
                      {event.contactEmail}
                    </a>
                  </div>
                )}

                {event.website && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={event.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline cursor-pointer"
                    >
                      Site web
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Localisation */}
            {!event.isVirtual && (
              <Card>
                <CardHeader>
                  <CardTitle>Localisation</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{event.location.city}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {event.location.region}, {event.location.country}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </main>
  );
}
