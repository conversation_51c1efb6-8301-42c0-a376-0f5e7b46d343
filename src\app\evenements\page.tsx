"use client";

import React, { useEffect, useState } from "react";
import { Calendar, TrendingUp, Users, Clock } from "lucide-react";
import { Layout } from "@/components/layout/Layout";
import { ContentCard } from "@/components/common/ContentCard";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { mockEvenements } from "@/data/mockData";
import { useFavorites } from "@/hooks/useLocalStorage";
import Link from "next/link";

export default function EvenementsPage() {
  const [isClient, setIsClient] = useState(false);
  const { value: favorites, setValue: setFavorites } = useFavorites();

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleFavoriteToggle = (id: string) => {
    const newFavorites = favorites.includes(id)
      ? favorites.filter((fav) => fav !== id)
      : [...favorites, id];
    setFavorites(newFavorites);
  };

  const stats = [
    {
      title: "Événements disponibles",
      value: "300+",
      icon: Calendar,
      description: "Partout en France",
    },
    {
      title: "Taux de satisfaction",
      value: "95%",
      icon: TrendingUp,
      description: "Des participants",
    },
    {
      title: "Participants annuels",
      value: "20,000+",
      icon: Users,
      description: "Dans tous les domaines",
    },
    {
      title: "Fréquence moyenne",
      value: "Par semaine",
      icon: Clock,
      description: "Dans chaque région",
    },
  ];

  const popularThemes = [
    { name: "Technologie", count: 75, color: "bg-blue-100 text-blue-800" },
    {
      name: "Entrepreneuriat",
      count: 60,
      color: "bg-green-100 text-green-800",
    },
    { name: "Carrière", count: 45, color: "bg-red-100 text-red-800" },
    { name: "Recherche", count: 40, color: "bg-purple-100 text-purple-800" },
    { name: "Innovation", count: 35, color: "bg-pink-100 text-pink-800" },
    {
      name: "Développement",
      count: 30,
      color: "bg-orange-100 text-orange-800",
    },
  ];

  return (
    <Layout showHeader showFooter>
      <section className="gradient-bg-hero py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-primary/10 rounded-full">
                <Calendar className="h-12 w-12 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Événements Professionnels
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Découvrez des conférences, ateliers et rencontres pour développer
              vos compétences, élargir votre réseau et booster votre carrière.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/search?types=evenement">
                <Button size="lg" className="cursor-pointer">
                  Explorer tous les événements
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="cursor-pointer">
                Conseils de participation
              </Button>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center card-elevated">
                <CardContent className="p-6">
                  <stat.icon className="h-10 w-10 mx-auto mb-4 text-primary" />
                  <div className="text-2xl font-bold mb-1">{stat.value}</div>
                  <div className="font-medium mb-1">{stat.title}</div>
                  <div className="text-sm text-muted-foreground">
                    {stat.description}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Thèmes populaires</h2>
            <p className="text-xl text-muted-foreground">
              Explorez les événements par domaine d&apos;expertise
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {popularThemes.map((theme, index) => (
              <Link
                key={index}
                href={{
                  pathname: "/search",
                  query: { types: "evenement", domain: theme.name },
                }}
                passHref
              >
                <Card className="card-interactive text-center h-full cursor-pointer">
                  <CardContent className="p-4">
                    <Badge className={`mb-2 ${theme.color}`}>
                      {theme.count} événements
                    </Badge>
                    <h3 className="font-semibold">{theme.name}</h3>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold mb-4">Événements en vedette</h2>
              <p className="text-xl text-muted-foreground">
                Sélection d&apos;événements de qualité recommandés par nos
                experts
              </p>
            </div>
            <Link href="/search?types=evenement">
              <Button variant="outline" className="cursor-pointer">
                Voir tous les événements
              </Button>
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockEvenements.map((evenement) => (
              <ContentCard
                key={evenement.id}
                item={evenement}
                variant="featured"
                onFavoriteToggle={() => handleFavoriteToggle(evenement.id)}
                isFavorite={isClient && favorites.includes(evenement.id)}
              />
            ))}
          </div>
        </div>
      </section>

      <section className="py-16 gradient-bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Prêt à développer votre réseau ?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Rencontrez des experts de votre domaine, participez à des ateliers
            pratiques et développez vos compétences lors de nos événements.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="cursor-pointer">
              Créer mon profil
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary cursor-pointer"
            >
              Conseils de participation
            </Button>
          </div>
        </div>
      </section>
    </Layout>
  );
}
