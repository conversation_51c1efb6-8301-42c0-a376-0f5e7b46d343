"use client";

import { Footer } from "@/components/layout/Footer";
import { Header } from "@/components/layout/Header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { mockFormations } from "@/data/mockData";
import { useFavorites } from "@/hooks/useLocalStorage";
import { formatDate, formatPrice } from "@/lib/utils";
import {
  ArrowLeft,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  Euro,
  Globe,
  GraduationCap,
  Mail,
  MapPin,
  Phone,
  Users,
} from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function FormationDetailPage() {
  const [isClient, setIsClient] = useState(false);
  const params = useParams();
  const formationId = params.id as string;
  const { value: favorites, setValue: setFavorites } = useFavorites();

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Trouver la formation correspondante
  const formation = mockFormations.find((f) => f.id === formationId);

  const handleFavoriteToggle = () => {
    if (!formationId) return;
    const newFavorites = favorites.includes(formationId)
      ? favorites.filter((fav) => fav !== formationId)
      : [...favorites, formationId];
    setFavorites(newFavorites);
  };

  const handleShare = async () => {
    if (!formation) return;
    try {
      if (navigator.share) {
        await navigator.share({
          title: formation.title,
          text: formation.description,
          url: window.location.href,
        });
      } else {
        // Fallback pour les navigateurs qui ne supportent pas l'API Share
        await navigator.clipboard.writeText(window.location.href);
        alert("Lien copié dans le presse-papier !");
      }
    } catch (error) {
      console.error("Erreur lors du partage :", error);
    }
  };

  if (!formation) {
    return (
      <main>
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">Formation non trouvée</h1>
          <p className="text-muted-foreground mb-8">
            La formation que vous recherchez nexiste pas ou a été supprimée.
          </p>
          <Link href="/formations" passHref>
            <Button>Retour aux formations</Button>
          </Link>
        </div>
        <Footer />
      </main>
    );
  }

  return (
    <main>
      <Header />
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link
            href="/formations"
            className="flex items-center text-muted-foreground hover:text-primary"
            passHref
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour aux formations
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Contenu principal */}
          <div className="lg:col-span-2 space-y-8">
            {/* En-tête */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <Badge variant="formation" className="mb-4">
                  {formation.type === "diplome"
                    ? "Diplôme"
                    : formation.type === "certification"
                    ? "Certification"
                    : "Formation"}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                {formation.title}
              </h1>

              <div className="flex items-center gap-4 text-muted-foreground mb-6">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  <span>{formation.institution}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>
                    {formation.location.city}, {formation.location.region}
                  </span>
                </div>
              </div>
            </div>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {formation.description}
                </p>
              </CardContent>
            </Card>

            {/* Compétences acquises */}
            {formation.skills && formation.skills.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Compétences acquises</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {formation.skills.map((skill, index) => (
                      <Badge key={index} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Prérequis */}
            {formation.prerequisites && formation.prerequisites.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Prérequis</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {formation.prerequisites.map((prerequisite, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-muted-foreground">
                          {prerequisite}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Informations pratiques */}
            <Card>
              <CardHeader>
                <CardTitle>Informations pratiques</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Date de début</div>
                        <div className="text-muted-foreground">
                          {formatDate(formation.startDate)}
                        </div>
                      </div>
                    </div>
                    {formation.endDate && (
                      <div className="flex items-center gap-3">
                        <Calendar className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">Date de fin</div>
                          <div className="text-muted-foreground">
                            {formatDate(formation.endDate)}
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Durée</div>
                        <div className="text-muted-foreground">
                          {formation.duration}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <GraduationCap className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Niveau</div>
                        <div className="text-muted-foreground">
                          {formation.level.toUpperCase()}
                        </div>
                      </div>
                    </div>
                    {formation.cost !== undefined && (
                      <div className="flex items-center gap-3">
                        <Euro className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">Coût</div>
                          <div className="text-muted-foreground">
                            {formation.cost === 0
                              ? "Gratuit"
                              : formatPrice(formation.cost)}
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Modalité</div>
                        <div className="text-muted-foreground">
                          {formation.isOnline ? "En ligne" : "Présentiel"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Actions */}
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Link href={formation.website || "#"} passHref>
                    <Button className="w-full" size="lg">
                      Candidater maintenant
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleFavoriteToggle}
                  >
                    {isClient && favorites.includes(formationId)
                      ? "Retirer des favoris"
                      : "Ajouter aux favoris"}
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleShare}
                  >
                    Partager
                  </Button>
                </div>

                {formation.applicationDeadline && (
                  <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                    <div className="text-sm font-medium text-orange-800">
                      Date limite de candidature
                    </div>
                    <div className="text-sm text-orange-600">
                      {formatDate(formation.applicationDeadline)}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="font-medium mb-2">
                    {formation.institution}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formation.location.address}
                    <br />
                    {formation.location.postalCode} {formation.location.city}
                  </div>
                </div>

                {formation.contactEmail && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={`mailto:${formation.contactEmail}`}
                      className="text-sm text-primary hover:underline"
                    >
                      {formation.contactEmail}
                    </a>
                  </div>
                )}

                {formation.contactPhone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={`tel:${formation.contactPhone}`}
                      className="text-sm text-primary hover:underline"
                    >
                      {formation.contactPhone}
                    </a>
                  </div>
                )}

                {formation.website && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={formation.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline"
                    >
                      Site web
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Localisation */}
            <Card>
              <CardHeader>
                <CardTitle>Localisation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{formation.location.city}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formation.location.region}, {formation.location.country}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <Footer />
    </main>
  );
}
