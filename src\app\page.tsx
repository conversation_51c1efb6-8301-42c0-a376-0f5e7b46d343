"use client";

import { ContentCard } from "@/components/common/ContentCard";
import { Layout } from "@/components/layout/Layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { MAIN_NAVIGATION, SITE_CONFIG } from "@/constants";
import {
  mockEvenements,
  mockFormations,
  mockSalons,
  mockStages,
} from "@/data/mockData";
import { useSearch } from "@/hooks/useSearch";
import {
  ArrowRight,
  Briefcase,
  Calendar,
  GraduationCap,
  Search,
  Star,
  TrendingUp,
  Users,
} from "lucide-react";
import Link from "next/link";
import React from "react";

export default function Home() {
  const [searchQuery, setSearchQuery] = React.useState("");
  const { search } = useSearch();

  const handleSearch = () => {
    if (searchQuery.trim()) {
      search({ query: searchQuery });
      // Rediriger vers la page de résultats
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Statistiques de la plateforme
  const stats = [
    {
      label: "Formations",
      value: "2,500+",
      icon: GraduationCap,
      color: "text-[oklch(var(--formation))]",
    },
    {
      label: "Stages",
      value: "1,800+",
      icon: Briefcase,
      color: "text-[oklch(var(--stage))]",
    },
    {
      label: "Salons d'emploi",
      value: "150+",
      icon: Users,
      color: "text-[oklch(var(--salon))]",
    },
    {
      label: "Événements",
      value: "300+",
      icon: Calendar,
      color: "text-[oklch(var(--evenement))]",
    },
  ];

  // Contenus récents pour la démonstration
  const recentContent = [
    ...mockFormations.slice(0, 2),
    ...mockStages.slice(0, 2),
    ...mockSalons.slice(0, 1),
    ...mockEvenements.slice(0, 1),
  ];

  return (
    <Layout>
      {/* Section Hero */}
      <section className="gradient-bg-hero py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6 animate-fade-in">
              Votre avenir commence ici
            </h1>
            <p className="text-xl lg:text-2xl text-muted-foreground mb-8 animate-slide-up">
              Découvrez les meilleures opportunités de formations, stages,
              salons d&apos;emploi et événements en France. Tout en un seul
              endroit.
            </p>

            {/* Barre de recherche principale */}
            <div className="max-w-2xl mx-auto mb-8 animate-scale-in">
              <div className="flex gap-2">
                <Input
                  type="search"
                  placeholder="Rechercher formations, stages, événements..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  startIcon={<Search className="h-5 w-5" />}
                  className="text-lg h-14"
                />
                <Button
                  onClick={handleSearch}
                  size="lg"
                  className="h-14 px-8 cursor-pointer"
                >
                  Rechercher
                </Button>
              </div>
            </div>

            {/* Navigation rapide */}
            <div className="flex flex-wrap justify-center gap-4 animate-slide-up">
              {MAIN_NAVIGATION.map((item) => (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant="outline"
                    size="lg"
                    className="h-12 cursor-pointer"
                  >
                    {item.label}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Section Statistiques */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center card-elevated">
                <CardContent className="p-6">
                  <stat.icon
                    className={`h-12 w-12 mx-auto mb-4 ${stat.color}`}
                  />
                  <div className="text-3xl font-bold mb-2">{stat.value}</div>
                  <div className="text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Section Avantages */}
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Pourquoi choisir {SITE_CONFIG.name} ?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              La plateforme de référence pour votre développement professionnel
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="card-elevated">
              <CardHeader>
                <Star className="h-12 w-12 text-primary mb-4" />
                <CardTitle>Contenu de qualité</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Toutes les opportunités sont vérifiées et mises à jour
                  régulièrement pour vous garantir des informations fiables et
                  actuelles.
                </p>
              </CardContent>
            </Card>

            <Card className="card-elevated">
              <CardHeader>
                <Search className="h-12 w-12 text-primary mb-4" />
                <CardTitle>Recherche intelligente</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Notre système de recherche avancé vous aide à trouver
                  exactement ce que vous cherchez selon vos critères et votre
                  localisation.
                </p>
              </CardContent>
            </Card>

            <Card className="card-elevated">
              <CardHeader>
                <TrendingUp className="h-12 w-12 text-primary mb-4" />
                <CardTitle>Toujours à jour</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Recevez les dernières opportunités en temps réel et ne manquez
                  plus jamais une deadline importante.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Section Contenus récents */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                Opportunités récentes
              </h2>
              <p className="text-xl text-muted-foreground">
                Découvrez les dernières offres ajoutées à notre plateforme
              </p>
            </div>
            <Link href="/search">
              <Button variant="outline" className="cursor-pointer">
                Voir tout
                <ArrowRight className="ml-2 h-4 w-4 " />
              </Button>
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentContent.map((item) => (
              <ContentCard
                key={item.id}
                item={item}
                variant="default"
                showActions={false}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Section CTA */}
      <section className="py-20 gradient-bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Prêt à donner un nouvel élan à votre carrière ?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Rejoignez des milliers d&apos;étudiants et professionnels qui
            utilisent déjà notre plateforme pour trouver leur prochaine
            opportunité.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/formations">
              <Button size="lg" variant="secondary" className="cursor-pointer">
                Explorer les formations
              </Button>
            </Link>
            <Link href="/stages">
              <Button
                size="lg"
                variant="outline"
                className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary cursor-pointer"
              >
                Trouver un stage
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </Layout>
  );
}
