"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import {
  MapPin,
  Clock,
  Calendar,
  Users,
  Mail,
  Phone,
  Globe,
  ArrowLeft,
  Briefcase,
} from "lucide-react";
import Link from "next/link";
import { Footer } from "@/components/layout/Footer";
import { Header } from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { mockSalons } from "@/data/mockData";
import { useFavorites } from "@/hooks/useLocalStorage";
import { formatDate } from "@/lib/utils";

export default function SalonDetailPage() {
  const [isMounted, setIsMounted] = useState(false);
  const params = useParams();
  const salonId = params.id as string;
  const { value: favorites, setValue: setFavorites } = useFavorites();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Trouver le salon correspondant
  const salon = mockSalons.find((s) => s.id === salonId);

  const handleFavoriteToggle = () => {
    if (!salonId) return;
    const newFavorites = favorites.includes(salonId)
      ? favorites.filter((fav) => fav !== salonId)
      : [...favorites, salonId];
    setFavorites(newFavorites);
  };

  const handleShare = async () => {
    if (!salon) return;
    try {
      if (navigator.share) {
        await navigator.share({
          title: salon.title,
          text: salon.description,
          url: window.location.href,
        });
      } else {
        await navigator.clipboard.writeText(window.location.href);
        alert("Lien copié dans le presse-papier !");
      }
    } catch (error) {
      console.error("Erreur lors du partage :", error);
    }
  };

  if (!isMounted) {
    return (
      <main>
        <Header />
        <div className="container mx-auto px-4 py-16">
          <div className="h-6 w-40 bg-gray-200 rounded mb-6 animate-pulse" />
          <div className="grid lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2 space-y-8">
              <div className="h-10 w-64 bg-gray-200 rounded mb-4 animate-pulse" />
              <div className="h-4 w-full bg-gray-200 rounded mb-2 animate-pulse" />
              <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
            </div>
            <div className="space-y-6">
              <div className="h-40 bg-gray-200 rounded animate-pulse" />
              <div className="h-40 bg-gray-200 rounded animate-pulse" />
              <div className="h-40 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        </div>
        <Footer />
      </main>
    );
  }

  if (!salon) {
    return (
      <main>
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">Salon non trouvé</h1>
          <p className="text-muted-foreground mb-8">
            Le salon que vous recherchez n'existe pas ou a été supprimé.
          </p>
          <Link href="/salons">
            <Button>Retour aux salons</Button>
          </Link>
        </div>
        <Footer />
      </main>
    );
  }

  return (
    <main>
      <Header />
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link
            href="/salons"
            className="flex items-center text-muted-foreground hover:text-primary cursor-pointer"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour aux salons
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Contenu principal */}
          <div className="lg:col-span-2 space-y-8">
            {/* En-tête */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <Badge variant="secondary" className="mb-4">
                  {salon.isVirtual ? "Virtuel" : "Présentiel"}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                {salon.title}
              </h1>

              <div className="flex items-center gap-4 text-muted-foreground mb-6">
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4" />
                  <span>{salon.organizer}</span>
                </div>
                {!salon.isVirtual && (
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <span>
                      {salon.location.city}, {salon.location.region}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Description</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {salon.description}
                </p>
              </CardContent>
            </Card>

            {/* Secteurs */}
            {salon.sectors && salon.sectors.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Secteurs représentés</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {salon.sectors.map((sector, index) => (
                      <Badge key={index} variant="secondary">
                        {sector}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Informations pratiques */}
            <Card>
              <CardHeader>
                <CardTitle>Informations pratiques</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Date de début</div>
                        <div className="text-muted-foreground">
                          {formatDate(salon.startDate)}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Date de fin</div>
                        <div className="text-muted-foreground">
                          {formatDate(salon.endDate)}
                        </div>
                      </div>
                    </div>

                    {salon.registrationDeadline && (
                      <div className="flex items-center gap-3">
                        <Clock className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">
                            Date limite d'inscription
                          </div>
                          <div className="text-muted-foreground">
                            {formatDate(salon.registrationDeadline)}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Public cible</div>
                        <div className="text-muted-foreground">
                          {salon.targetAudience.join(", ")}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Entreprises attendues</div>
                        <div className="text-muted-foreground">
                          {salon.expectedCompanies}
                        </div>
                      </div>
                    </div>

                    {salon.registrationFee !== undefined && (
                      <div className="flex items-center gap-3">
                        <Clock className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">Frais d'inscription</div>
                          <div className="text-muted-foreground">
                            {salon.registrationFee === 0
                              ? "Gratuit"
                              : `${salon.registrationFee} €`}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Actions */}
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Link
                    href={salon.registrationUrl || "#"}
                    className="cursor-pointer"
                  >
                    <Button className="w-full" size="lg">
                      S'inscrire au salon
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleFavoriteToggle}
                  >
                    {favorites.includes(salonId)
                      ? "Retirer des favoris"
                      : "Ajouter aux favoris"}
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleShare}
                  >
                    Partager
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Organisateur</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="font-medium mb-2">{salon.organizer}</div>
                  {!salon.isVirtual && (
                    <div className="text-sm text-muted-foreground">
                      {salon.location.address}
                      <br />
                      {salon.location.postalCode} {salon.location.city}
                    </div>
                  )}
                </div>

                {salon.website && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={salon.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline cursor-pointer"
                    >
                      Site web
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Localisation */}
            {!salon.isVirtual && (
              <Card>
                <CardHeader>
                  <CardTitle>Localisation</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{salon.location.city}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {salon.location.region}, {salon.location.country}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
      <Footer />
    </main>
  );
}
