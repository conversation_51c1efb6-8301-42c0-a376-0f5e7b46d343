"use client";

import React, { useEffect, useState } from "react";
import { MapPin, TrendingUp, Users, Clock } from "lucide-react";
import { Layout } from "@/components/layout/Layout";
import { ContentCard } from "@/components/common/ContentCard";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { mockSalons } from "@/data/mockData";
import { useFavorites } from "@/hooks/useLocalStorage";
import Link from "next/link";

export default function SalonsPage() {
  const [isClient, setIsClient] = useState(false);
  const { value: favorites, setValue: setFavorites } = useFavorites();

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleFavoriteToggle = (id: string) => {
    const newFavorites = favorites.includes(id)
      ? favorites.filter((fav) => fav !== id)
      : [...favorites, id];
    setFavorites(newFavorites);
  };

  // Statistiques des salons
  const stats = [
    {
      title: "Salons disponibles",
      value: "150+",
      icon: MapPin,
      description: "Partout en France",
    },
    {
      title: "Taux de satisfaction",
      value: "92%",
      icon: TrendingUp,
      description: "Des participants",
    },
    {
      title: "Entreprises participantes",
      value: "5,000+",
      icon: Users,
      description: "De tous secteurs",
    },
    {
      title: "Fréquence moyenne",
      value: "Par mois",
      icon: Clock,
      description: "Dans chaque région",
    },
  ];

  // Secteurs populaires
  const popularSectors = [
    { name: "Technologie", count: 45, color: "bg-blue-100 text-blue-800" },
    { name: "Santé", count: 32, color: "bg-green-100 text-green-800" },
    { name: "Commerce", count: 28, color: "bg-red-100 text-red-800" },
    { name: "Ingénierie", count: 24, color: "bg-purple-100 text-purple-800" },
    { name: "Finance", count: 20, color: "bg-pink-100 text-pink-800" },
    { name: "Éducation", count: 18, color: "bg-orange-100 text-orange-800" },
  ];

  return (
    <Layout showHeader={true} showFooter={true}>
      {/* Hero Section */}
      <section className="gradient-bg-hero py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-primary/10 rounded-full">
                <MapPin className="h-12 w-12 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Salons d'Emploi et Forums
            </h1>
            <p className="text-xl text-muted-foreground mb-8">
              Découvrez les meilleurs salons professionnels pour rencontrer des
              recruteurs, trouver des opportunités et développer votre réseau.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/search?type=salon">
                <Button size="lg" className="cursor-pointer">
                  Explorer tous les salons
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="cursor-pointer">
                Conseils de préparation
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Statistiques */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center card-elevated">
                <CardContent className="p-6">
                  <stat.icon className="h-10 w-10 mx-auto mb-4 text-primary" />
                  <div className="text-2xl font-bold mb-1">{stat.value}</div>
                  <div className="font-medium mb-1">{stat.title}</div>
                  <div className="text-sm text-muted-foreground">
                    {stat.description}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Secteurs populaires */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Secteurs populaires</h2>
            <p className="text-xl text-muted-foreground">
              Explorez les salons par domaine d'activité
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {popularSectors.map((sector, index) => (
              <Link
                key={index}
                href={{
                  pathname: "/search",
                  query: {
                    type: "salon",
                    domain: sector.name,
                    q: "",
                  },
                }}
                passHref
              >
                <Card className="card-interactive text-center h-full cursor-pointer">
                  <CardContent className="p-4">
                    <Badge className={`mb-2 ${sector.color}`}>
                      {sector.count} salons
                    </Badge>
                    <h3 className="font-semibold">{sector.name}</h3>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Salons en vedette */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold mb-4">Salons en vedette</h2>
              <p className="text-xl text-muted-foreground">
                Sélection de salons de qualité recommandés par nos experts
              </p>
            </div>
            <Link
              href={{
                pathname: "/search",
                query: {
                  type: "salon",
                  q: "",
                },
              }}
              passHref
            >
              <Button variant="outline" className="cursor-pointer">
                Voir tous les salons
              </Button>
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockSalons.map((salon) => (
              <ContentCard
                key={salon.id}
                item={salon}
                variant="featured"
                onFavoriteToggle={() => handleFavoriteToggle(salon.id)}
                isFavorite={isClient && favorites.includes(salon.id)}
              />
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 gradient-bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">
            Prêt à booster votre carrière ?
          </h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Rencontrez des recruteurs des plus grandes entreprises et découvrez
            des opportunités uniques lors de nos salons professionnels.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="cursor-pointer">
              Préparer mon CV
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary cursor-pointer"
            >
              Conseils de networking
            </Button>
          </div>
        </div>
      </section>
    </Layout>
  );
}
