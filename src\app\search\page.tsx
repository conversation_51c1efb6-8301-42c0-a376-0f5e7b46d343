"use client";

import { Layout } from "@/components/layout/Layout";
import { SearchFilters } from "@/components/search/SearchFilters";
import { SearchResults } from "@/components/search/SearchResults";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useFavorites } from "@/hooks/useLocalStorage";
import { useSearch } from "@/hooks/useSearch";
import { Search } from "lucide-react";
import { useSearchParams, useRouter } from "next/navigation";
import React, { Suspense, useEffect, useMemo } from "react";
import { ContentType } from "@/types";

function SearchPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const initialQuery = searchParams.get("q") || "";
  const initialTypes = searchParams.getAll("types") as ContentType[];
  const initialDomain = searchParams.get("domain") || "";
  const initialLocation = searchParams.get("location") || "";

  const initialFilters = useMemo(() => {
    const filters: any = { query: initialQuery };

    if (initialTypes.length > 0) {
      filters.types = initialTypes;
    }

    if (initialDomain) {
      filters.domains = [initialDomain];
    }

    if (initialLocation) {
      const [city, region] = initialLocation.split("|");
      filters.location = {
        city: city || undefined,
        region: region || undefined,
      };
    }

    return filters;
  }, [initialQuery, initialTypes, initialDomain, initialLocation]);

  const [searchQuery, setSearchQuery] = React.useState(initialQuery);
  const [isFiltersCollapsed, setIsFiltersCollapsed] = React.useState(false);
  const [viewMode, setViewMode] = React.useState<"grid" | "list">("grid");

  const { value: favorites, setValue: setFavorites } = useFavorites();

  const {
    results,
    isLoading,
    error,
    filters,
    search,
    updateFilters,
    clearFilters,
    loadMore,
    hasMore,
  } = useSearch({
    initialFilters,
    autoSearch: true,
  });

  const handleSearch = () => {
    const params = new URLSearchParams();

    if (searchQuery) params.set("q", searchQuery);

    if (filters.types?.length) {
      filters.types.forEach((type) => {
        params.append("types", type);
      });
    }

    if (filters.domains?.length) {
      params.set("domain", filters.domains[0]);
    }

    if (filters.location) {
      const locationStr = [
        filters.location.city || "",
        filters.location.region || "",
      ]
        .filter(Boolean)
        .join("|");

      if (locationStr) params.set("location", locationStr);
    }

    router.push(`/search?${params.toString()}`);
    search({ query: searchQuery });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") handleSearch();
  };

  const handleFavoriteToggle = (id: string) => {
    const newFavorites = favorites.includes(id)
      ? favorites.filter((fav) => fav !== id)
      : [...favorites, id];
    setFavorites(newFavorites);
  };

  useEffect(() => {
    setSearchQuery(initialQuery);
  }, [initialQuery]);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="max-w-2xl mx-auto">
            <div className="flex gap-2">
              <Input
                type="search"
                placeholder="Rechercher formations, stages, événements..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                startIcon={<Search className="h-5 w-5" />}
                className="text-lg h-12"
              />
              <Button onClick={handleSearch} size="lg" className="h-12 px-6">
                Rechercher
              </Button>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-24">
              <SearchFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
                isCollapsed={isFiltersCollapsed}
                onToggleCollapse={() =>
                  setIsFiltersCollapsed(!isFiltersCollapsed)
                }
                className="lg:block"
              />
            </div>
          </div>

          <div className="lg:col-span-3">
            <div className="lg:hidden mb-6">
              <SearchFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
                isCollapsed={true}
                onToggleCollapse={() =>
                  setIsFiltersCollapsed(!isFiltersCollapsed)
                }
              />
            </div>

            {isFiltersCollapsed && (
              <div className="lg:hidden mb-6">
                <SearchFilters
                  filters={filters}
                  onFiltersChange={updateFilters}
                  onClearFilters={clearFilters}
                  isCollapsed={false}
                  onToggleCollapse={() => setIsFiltersCollapsed(false)}
                />
              </div>
            )}

            <SearchResults
              results={results}
              isLoading={isLoading}
              error={error}
              onLoadMore={hasMore ? loadMore : undefined}
              onFavoriteToggle={handleFavoriteToggle}
              favorites={favorites}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default function SearchPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          Chargement...
        </div>
      }
    >
      <SearchPageContent />
    </Suspense>
  );
}
