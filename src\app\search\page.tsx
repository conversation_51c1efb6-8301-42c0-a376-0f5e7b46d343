"use client";

import { Layout } from "@/components/layout/Layout";
import { SearchFilters } from "@/components/search/SearchFilters";
import { SearchResults } from "@/components/search/SearchResults";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useFavorites } from "@/hooks/useLocalStorage";
import { useSearch } from "@/hooks/useSearch";
import { Search } from "lucide-react";
import { useSearchParams, useRouter } from "next/navigation";
import React, { Suspense, useEffect, useMemo } from "react";
import { ContentType } from "@/types";

function SearchPageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Récupérer tous les paramètres de l'URL
  const initialQuery = searchParams.get("q") || "";
  const initialTypes = searchParams.getAll("type") as ContentType[];
  const initialDomain = searchParams.get("domain") || "";
  const initialLocation = searchParams.get("location") || "";

  // Créer un objet de filtres initiaux basé sur l'URL
  const initialFilters = useMemo(() => {
    const filters: any = { query: initialQuery };

    if (initialTypes.length > 0) {
      filters.type = initialTypes;
    }

    if (initialDomain) {
      filters.domain = [initialDomain];
    }

    if (initialLocation) {
      const [city, region] = initialLocation.split("|");
      filters.location = {
        city: city || undefined,
        region: region || undefined,
      };
    }

    return filters;
  }, [initialQuery, initialTypes, initialDomain, initialLocation]);

  const [searchQuery, setSearchQuery] = React.useState(initialQuery);
  const [isFiltersCollapsed, setIsFiltersCollapsed] = React.useState(false);
  const [viewMode, setViewMode] = React.useState<"grid" | "list">("grid");

  const { value: favorites, setValue: setFavorites } = useFavorites();

  const {
    results,
    isLoading,
    error,
    filters,
    search,
    updateFilters,
    clearFilters,
    loadMore,
    hasMore,
  } = useSearch({
    initialFilters,
    autoSearch: true,
  });

  const handleSearch = () => {
    const params = new URLSearchParams();

    // Ajouter la requête
    if (searchQuery) params.set("q", searchQuery);

    // Ajouter les types sélectionnés
    if (filters.type?.length) {
      filters.type.forEach((type) => {
        params.append("type", type);
      });
    }

    // Ajouter le domaine sélectionné
    if (filters.domain?.length) {
      params.set("domain", filters.domain[0]);
    }

    // Ajouter la localisation
    if (filters.location) {
      const locationStr = [
        filters.location.city || "",
        filters.location.region || "",
      ]
        .filter(Boolean)
        .join("|");

      if (locationStr) params.set("location", locationStr);
    }

    router.push(`/search?${params.toString()}`);
    search({ query: searchQuery });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") handleSearch();
  };

  const handleFavoriteToggle = (id: string) => {
    const newFavorites = favorites.includes(id)
      ? favorites.filter((fav) => fav !== id)
      : [...favorites, id];
    setFavorites(newFavorites);
  };

  // Synchronisation avec l'URL
  useEffect(() => {
    setSearchQuery(initialQuery);
  }, [initialQuery]);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        {/* Barre de recherche */}
        <div className="mb-8">
          <div className="max-w-2xl mx-auto">
            <div className="flex gap-2">
              <Input
                type="search"
                placeholder="Rechercher formations, stages, événements..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                startIcon={<Search className="h-5 w-5" />}
                className="text-lg h-12"
              />
              <Button onClick={handleSearch} size="lg" className="h-12 px-6">
                Rechercher
              </Button>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Sidebar des filtres */}
          <div className="lg:col-span-1">
            <div className="lg:sticky lg:top-24">
              <SearchFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
                isCollapsed={isFiltersCollapsed}
                onToggleCollapse={() =>
                  setIsFiltersCollapsed(!isFiltersCollapsed)
                }
                className="lg:block"
              />
            </div>
          </div>

          {/* Zone des résultats */}
          <div className="lg:col-span-3">
            {/* Filtres mobiles */}
            <div className="lg:hidden mb-6">
              <SearchFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onClearFilters={clearFilters}
                isCollapsed={true}
                onToggleCollapse={() =>
                  setIsFiltersCollapsed(!isFiltersCollapsed)
                }
              />
            </div>

            {/* Filtres mobiles expandés */}
            {isFiltersCollapsed && (
              <div className="lg:hidden mb-6">
                <SearchFilters
                  filters={filters}
                  onFiltersChange={updateFilters}
                  onClearFilters={clearFilters}
                  isCollapsed={false}
                  onToggleCollapse={() => setIsFiltersCollapsed(false)}
                />
              </div>
            )}

            {/* Résultats */}
            <SearchResults
              results={results}
              isLoading={isLoading}
              error={error}
              onLoadMore={hasMore ? loadMore : undefined}
              onFavoriteToggle={handleFavoriteToggle}
              favorites={favorites}
              viewMode={viewMode}
              onViewModeChange={setViewMode}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default function SearchPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          Chargement...
        </div>
      }
    >
      <SearchPageContent />
    </Suspense>
  );
}
