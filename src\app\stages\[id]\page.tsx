"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import {
  MapPin,
  Clock,
  Euro,
  Calendar,
  Briefcase,
  User,
  Mail,
  Phone,
  Globe,
  ArrowLeft,
} from "lucide-react";
import Link from "next/link";
import { Footer } from "@/components/layout/Footer";
import { Header } from "@/components/layout/Header";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { mockStages } from "@/data/mockData";
import { useFavorites } from "@/hooks/useLocalStorage";
import { formatDate } from "@/lib/utils";

export default function StageDetailPage() {
  const [isClient, setIsClient] = useState(false);
  const params = useParams();
  const stageId = params.id as string;
  const { value: favorites, setValue: setFavorites } = useFavorites();

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Trouver le stage correspondant
  const stage = mockStages.find((s) => s.id === stageId);

  const handleFavoriteToggle = () => {
    if (!stageId) return;
    const newFavorites = favorites.includes(stageId)
      ? favorites.filter((fav) => fav !== stageId)
      : [...favorites, stageId];
    setFavorites(newFavorites);
  };

  const handleShare = async () => {
    if (!stage) return;
    try {
      if (navigator.share) {
        await navigator.share({
          title: stage.title,
          text: stage.description,
          url: window.location.href,
        });
      } else {
        await navigator.clipboard.writeText(window.location.href);
        alert("Lien copié dans le presse-papier !");
      }
    } catch (error) {
      console.error("Erreur lors du partage :", error);
    }
  };

  if (!stage) {
    return (
      <main>
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">Stage non trouvé</h1>
          <p className="text-muted-foreground mb-8">
            Le stage que vous recherchez nexiste pas ou a été supprimé.
          </p>
          <Link href="/stages">
            <Button>Retour aux stages</Button>
          </Link>
        </div>
        <Footer />
      </main>
    );
  }

  return (
    <main>
      <Header />
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <Link
            href="/stages"
            className="flex items-center text-muted-foreground hover:text-primary cursor-pointer"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour aux stages
          </Link>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Contenu principal */}
          <div className="lg:col-span-2 space-y-8">
            {/* En-tête */}
            <div>
              <div className="flex items-start justify-between mb-4">
                <Badge variant="secondary" className="mb-4">
                  {stage.level.toUpperCase()}
                </Badge>
              </div>

              <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                {stage.title}
              </h1>

              <div className="flex items-center gap-4 text-muted-foreground mb-6">
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4" />
                  <span>{stage.company}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>
                    {stage.location.city}, {stage.location.region}
                  </span>
                </div>
              </div>
            </div>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>Description du poste</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  {stage.description}
                </p>
              </CardContent>
            </Card>

            {/* Compétences requises */}
            {stage.skills && stage.skills.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Compétences requises</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {stage.skills.map((skill, index) => (
                      <Badge key={index} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Avantages */}
            {stage.benefits && stage.benefits.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Avantages</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {stage.benefits.map((benefit, index) => (
                      <li
                        key={index}
                        className="flex items-start gap-2 text-muted-foreground"
                      >
                        <div className="bg-green-500/10 rounded-full p-1">
                          <User className="h-4 w-4 text-green-500" />
                        </div>
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Informations pratiques */}
            <Card>
              <CardHeader>
                <CardTitle>Informations pratiques</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Date de début</div>
                        <div className="text-muted-foreground">
                          {formatDate(stage.startDate)}
                        </div>
                      </div>
                    </div>

                    {stage.endDate && (
                      <div className="flex items-center gap-3">
                        <Calendar className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">Date de fin</div>
                          <div className="text-muted-foreground">
                            {formatDate(stage.endDate)}
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Durée</div>
                        <div className="text-muted-foreground">
                          {stage.duration}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Briefcase className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Poste</div>
                        <div className="text-muted-foreground">
                          {stage.position}
                        </div>
                      </div>
                    </div>

                    {stage.salary !== undefined && (
                      <div className="flex items-center gap-3">
                        <Euro className="h-5 w-5 text-primary" />
                        <div>
                          <div className="font-medium">Rémunération</div>
                          <div className="text-muted-foreground">
                            {stage.salary} €/mois
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium">Modalité</div>
                        <div className="text-muted-foreground">
                          {stage.isRemote ? "Télétravail" : "Présentiel"}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Actions */}
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Link
                    href={stage.applicationUrl || "#"}
                    className="cursor-pointer"
                  >
                    <Button className="w-full" size="lg">
                      Postuler maintenant
                    </Button>
                  </Link>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleFavoriteToggle}
                  >
                    {favorites.includes(stageId)
                      ? "Retirer des favoris"
                      : "Ajouter aux favoris"}
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleShare}
                  >
                    Partager
                  </Button>
                </div>

                {stage.applicationDeadline && (
                  <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
                    <div className="text-sm font-medium text-orange-800">
                      Date limite de candidature
                    </div>
                    <div className="text-sm text-orange-600">
                      {formatDate(stage.applicationDeadline)}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="font-medium mb-2">{stage.company}</div>
                  <div className="text-sm text-muted-foreground">
                    {stage.location.address}
                    <br />
                    {stage.location.postalCode} {stage.location.city}
                  </div>
                </div>

                {stage.contactEmail && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={`mailto:${stage.contactEmail}`}
                      className="text-sm text-primary hover:underline cursor-pointer"
                    >
                      {stage.contactEmail}
                    </a>
                  </div>
                )}

                {stage.contactEmail && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={`tel:${stage.contactEmail}`}
                      className="text-sm text-primary hover:underline cursor-pointer"
                    >
                      {stage.contactEmail}
                    </a>
                  </div>
                )}

                {stage.website && (
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={stage.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sm text-primary hover:underline cursor-pointer"
                    >
                      Site web
                    </a>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Localisation */}
            <Card>
              <CardHeader>
                <CardTitle>Localisation</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{stage.location.city}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stage.location.region}, {stage.location.country}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <Footer />
    </main>
  );
}
