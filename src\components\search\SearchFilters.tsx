"use client";

import React, { useEffect } from "react";
import {
  X,
  Filter,
  MapPin,
  Calendar,
  Briefcase,
  GraduationCap,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SearchFilters as SearchFiltersType, ContentType } from "@/types";
import {
  DOMAINS,
  EDUCATION_LEVELS,
  FRENCH_REGIONS,
  SORT_OPTIONS,
} from "@/constants";
import { cn } from "@/lib/utils";

interface SearchFiltersProps {
  filters: SearchFiltersType;
  onFiltersChange: (filters: Partial<SearchFiltersType>) => void;
  onClearFilters: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
  className?: string;
}

export function SearchFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  isCollapsed = false,
  onToggleCollapse,
  className,
}: SearchFiltersProps) {
  const handleTypeChange = (type: ContentType, checked: boolean) => {
    const currentTypes = filters.type || [];
    const newTypes = checked
      ? [...currentTypes, type]
      : currentTypes.filter((t) => t !== type);

    onFiltersChange({ type: newTypes.length > 0 ? newTypes : undefined });
  };

  const handleDomainChange = (domain: string, checked: boolean) => {
    const currentDomains = filters.domain || [];
    const newDomains = checked
      ? [...currentDomains, domain]
      : currentDomains.filter((d) => d !== domain);

    onFiltersChange({ domain: newDomains.length > 0 ? newDomains : undefined });
  };

  const handleLevelChange = (level: string, checked: boolean) => {
    const currentLevels = filters.level || [];
    const newLevels = checked
      ? [...currentLevels, level]
      : currentLevels.filter((l) => l !== level);

    onFiltersChange({ level: newLevels.length > 0 ? newLevels : undefined });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.type?.length) count++;
    if (filters.location?.city) count++;
    if (filters.location?.region) count++;
    if (filters.domain?.length) count++;
    if (filters.level?.length) count++;
    if (filters.isRemote !== undefined) count++;
    if (filters.isFree !== undefined) count++;
    return count;
  };

  const contentTypes: Array<{
    value: ContentType;
    label: string;
    icon: React.ReactNode;
  }> = [
    {
      value: "formation",
      label: "Formations",
      icon: <GraduationCap className="h-4 w-4" />,
    },
    {
      value: "stage",
      label: "Stages",
      icon: <Briefcase className="h-4 w-4" />,
    },
    {
      value: "salon",
      label: "Salons d'emploi",
      icon: <MapPin className="h-4 w-4" />,
    },
    {
      value: "evenement",
      label: "Événements",
      icon: <Calendar className="h-4 w-4" />,
    },
  ];

  // Synchroniser les cases à cocher au montage
  useEffect(() => {
    // Synchroniser les types
    if (filters.type && filters.type.length > 0) {
      contentTypes.forEach((type) => {
        const shouldBeChecked = filters.type.includes(type.value);
        const input = document.querySelector(`input[value="${type.value}"]`);

        if (input && (input as HTMLInputElement).checked !== shouldBeChecked) {
          (input as HTMLInputElement).checked = shouldBeChecked;
        }
      });
    }

    // Synchroniser les domaines
    if (filters.domain && filters.domain.length > 0) {
      DOMAINS.forEach((domain) => {
        const shouldBeChecked = filters.domain.includes(domain);
        const inputs = document.querySelectorAll(
          `input[data-domain="${domain}"]`
        );

        inputs.forEach((input) => {
          if ((input as HTMLInputElement).checked !== shouldBeChecked) {
            (input as HTMLInputElement).checked = shouldBeChecked;
          }
        });
      });
    }
  }, [filters.type, filters.domain]);

  if (isCollapsed) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Button
          variant="outline"
          onClick={onToggleCollapse}
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          Filtres
          {getActiveFiltersCount() > 0 && (
            <Badge variant="secondary" className="ml-1">
              {getActiveFiltersCount()}
            </Badge>
          )}
        </Button>
        {getActiveFiltersCount() > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-muted-foreground"
          >
            Effacer tout
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtres de recherche
          </CardTitle>
          <div className="flex items-center gap-2">
            {getActiveFiltersCount() > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="text-muted-foreground"
              >
                Effacer tout
              </Button>
            )}
            {onToggleCollapse && (
              <Button variant="ghost" size="sm" onClick={onToggleCollapse}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Types de contenu */}
        <div>
          <h3 className="font-medium mb-3">Type de contenu</h3>
          <div className="grid grid-cols-2 gap-2">
            {contentTypes.map((type) => (
              <label
                key={type.value}
                className="flex items-center space-x-2 cursor-pointer p-2 rounded-md hover:bg-accent"
              >
                <input
                  type="checkbox"
                  checked={filters.type?.includes(type.value) || false}
                  onChange={(e) =>
                    handleTypeChange(type.value, e.target.checked)
                  }
                  className="rounded border-gray-300"
                  value={type.value}
                />
                <div className="flex items-center gap-2">
                  {type.icon}
                  <span className="text-sm">{type.label}</span>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Localisation */}
        <div>
          <h3 className="font-medium mb-3">Localisation</h3>
          <div className="space-y-3">
            <Input
              placeholder="Ville"
              value={filters.location?.city || ""}
              onChange={(e) =>
                onFiltersChange({
                  location: {
                    ...filters.location,
                    city: e.target.value || undefined,
                  },
                })
              }
            />
            <Select
              value={filters.location?.region || ""}
              onValueChange={(value) =>
                onFiltersChange({
                  location: { ...filters.location, region: value || undefined },
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Région" />
              </SelectTrigger>
              <SelectContent>
                {FRENCH_REGIONS.map((region) => (
                  <SelectItem key={region} value={region}>
                    {region}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Domaines */}
        <div>
          <h3 className="font-medium mb-3">Domaines</h3>
          <div className="max-h-40 overflow-y-auto space-y-1">
            {DOMAINS.map((domain) => (
              <label
                key={domain}
                className="flex items-center space-x-2 cursor-pointer p-1 rounded hover:bg-accent"
              >
                <input
                  type="checkbox"
                  checked={filters.domain?.includes(domain) || false}
                  onChange={(e) => handleDomainChange(domain, e.target.checked)}
                  className="rounded border-gray-300"
                  data-domain={domain}
                />
                <span className="text-sm">{domain}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Niveaux d'études */}
        <div>
          <h3 className="font-medium mb-3">Niveau d'études</h3>
          <div className="space-y-1">
            {EDUCATION_LEVELS.map((level) => (
              <label
                key={level.value}
                className="flex items-center space-x-2 cursor-pointer p-1 rounded hover:bg-accent"
              >
                <input
                  type="checkbox"
                  checked={filters.level?.includes(level.value) || false}
                  onChange={(e) =>
                    handleLevelChange(level.value, e.target.checked)
                  }
                  className="rounded border-gray-300"
                />
                <span className="text-sm">{level.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Options supplémentaires */}
        <div>
          <h3 className="font-medium mb-3">Options</h3>
          <div className="space-y-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.isRemote || false}
                onChange={(e) =>
                  onFiltersChange({
                    isRemote: e.target.checked || undefined,
                  })
                }
                className="rounded border-gray-300"
              />
              <span className="text-sm">Télétravail / À distance</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.isFree || false}
                onChange={(e) =>
                  onFiltersChange({
                    isFree: e.target.checked || undefined,
                  })
                }
                className="rounded border-gray-300"
              />
              <span className="text-sm">Gratuit</span>
            </label>
          </div>
        </div>

        {/* Tri */}
        <div>
          <h3 className="font-medium mb-3">Trier par</h3>
          <Select
            value={filters.sortBy || "relevance"}
            onValueChange={(value) =>
              onFiltersChange({
                sortBy: value as any,
              })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
}
