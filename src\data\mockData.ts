import { Formation, Stage, SalonEmploi, Evenement } from "@/types";

// Données de démonstration pour les formations
export const mockFormations: Formation[] = [
  {
    id: "formation-1",
    title: "Master en Intelligence Artificielle",
    description:
      "Formation complète en IA couvrant le machine learning, deep learning, et les applications pratiques. Programme intensif avec projets réels et stages en entreprise.",
    type: "diplome",
    level: "bac+5",
    domain: "Informatique",
    institution: "École Polytechnique",
    location: {
      address: "91 Route de Saclay",
      city: "Palaiseau",
      postalCode: "91120",
      region: "Île-de-France",
      country: "France",
      coordinates: { lat: 48.7144, lng: 2.2147 },
    },
    duration: "2 ans",
    startDate: new Date("2024-09-01"),
    endDate: new Date("2026-06-30"),
    applicationDeadline: new Date("2024-05-15"),
    cost: 15000,
    isOnline: false,
    prerequisites: ["Licence en informatique", "Mathématiques niveau L3"],
    skills: [
      "Python",
      "TensorFlow",
      "Machine Learning",
      "Deep Learning",
      "Data Science",
    ],
    website: "https://www.polytechnique.edu",
    contactEmail: "<EMAIL>",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-20"),
    isActive: true,
  },
  {
    id: "formation-2",
    title: "Certification Développement Web Full-Stack",
    description:
      "Apprenez à créer des applications web modernes avec React, Node.js, et les dernières technologies. Formation pratique avec projets portfolio.",
    type: "certification",
    level: "bac+2",
    domain: "Informatique",
    institution: "OpenClassrooms",
    location: {
      address: "En ligne",
      city: "Paris",
      postalCode: "75000",
      region: "Île-de-France",
      country: "France",
    },
    duration: "6 mois",
    startDate: new Date("2024-03-01"),
    applicationDeadline: new Date("2024-02-15"),
    cost: 3000,
    isOnline: true,
    skills: ["JavaScript", "React", "Node.js", "MongoDB", "Git"],
    website: "https://openclassrooms.com",
    contactEmail: "<EMAIL>",
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-18"),
    isActive: true,
  },
  {
    id: "formation-3",
    title: "BTS Commerce International",
    description:
      "Formation en commerce international avec focus sur les marchés européens et asiatiques. Stages obligatoires à l'étranger.",
    type: "diplome",
    level: "bac+2",
    domain: "Commerce",
    institution: "Lycée Jean Monnet",
    location: {
      address: "15 Avenue Jean Monnet",
      city: "Lyon",
      postalCode: "69003",
      region: "Auvergne-Rhône-Alpes",
      country: "France",
    },
    duration: "2 ans",
    startDate: new Date("2024-09-01"),
    endDate: new Date("2026-06-30"),
    applicationDeadline: new Date("2024-04-30"),
    cost: 0,
    isOnline: false,
    skills: [
      "Négociation",
      "Anglais",
      "Espagnol",
      "Marketing international",
      "Logistique",
    ],
    contactEmail: "<EMAIL>",
    createdAt: new Date("2024-01-12"),
    updatedAt: new Date("2024-01-19"),
    isActive: true,
  },
];

// Données de démonstration pour les stages
export const mockStages: Stage[] = [
  {
    id: "stage-1",
    title: "Stage Développeur Frontend React",
    description:
      "Rejoignez notre équipe de développement pour créer des interfaces utilisateur modernes et performantes. Vous travaillerez sur des projets innovants avec les dernières technologies.",
    company: "TechCorp",
    position: "Développeur Frontend",
    domain: "Informatique",
    location: {
      address: "42 Avenue des Champs-Élysées",
      city: "Paris",
      postalCode: "75008",
      region: "Île-de-France",
      country: "France",
    },
    duration: "6 mois",
    startDate: new Date("2024-04-01"),
    endDate: new Date("2024-09-30"),
    applicationDeadline: new Date("2024-03-15"),
    salary: 1200,
    level: "bac+3",
    isRemote: false,
    skills: ["React", "TypeScript", "CSS", "Git", "Agile"],
    benefits: [
      "Tickets restaurant",
      "Transport remboursé",
      "Télétravail partiel",
    ],
    website: "https://techcorp.com",
    contactEmail: "<EMAIL>",
    applicationUrl: "https://techcorp.com/careers/stage-frontend",
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-16"),
    isActive: true,
  },
  {
    id: "stage-2",
    title: "Stage Marketing Digital",
    description:
      "Participez à nos campagnes marketing digital et apprenez les stratégies de croissance. Expérience pratique avec les outils professionnels.",
    company: "Digital Agency",
    position: "Assistant Marketing",
    domain: "Marketing",
    location: {
      address: "25 Rue de la République",
      city: "Marseille",
      postalCode: "13001",
      region: "Provence-Alpes-Côte d'Azur",
      country: "France",
    },
    duration: "4 mois",
    startDate: new Date("2024-05-01"),
    endDate: new Date("2024-08-31"),
    applicationDeadline: new Date("2024-04-01"),
    salary: 800,
    level: "bac+2",
    isRemote: true,
    skills: [
      "Google Ads",
      "Facebook Ads",
      "Analytics",
      "SEO",
      "Content Marketing",
    ],
    benefits: ["Formation certifiante", "Matériel fourni"],
    contactEmail: "<EMAIL>",
    createdAt: new Date("2024-01-14"),
    updatedAt: new Date("2024-01-21"),
    isActive: true,
  },
];

// Données de démonstration pour les salons d'emploi
export const mockSalons: SalonEmploi[] = [
  {
    id: "salon-1",
    title: "Salon de l'Emploi Tech 2024",
    description:
      "Le plus grand salon dédié aux métiers du numérique. Rencontrez les recruteurs des plus grandes entreprises tech françaises et internationales.",
    organizer: "TechEvents France",
    location: {
      address: "Parc des Expositions",
      city: "Paris",
      postalCode: "75015",
      region: "Île-de-France",
      country: "France",
    },
    startDate: new Date("2024-04-15"),
    endDate: new Date("2024-04-16"),
    registrationDeadline: new Date("2024-04-10"),
    targetAudience: [
      "Développeurs",
      "Data Scientists",
      "Product Managers",
      "Designers",
    ],
    sectors: ["Informatique", "Intelligence Artificielle", "Cybersécurité"],
    expectedCompanies: 150,
    isVirtual: false,
    registrationFee: 0,
    website: "https://salon-emploi-tech.fr",
    registrationUrl: "https://salon-emploi-tech.fr/inscription",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-22"),
    isActive: true,
  },
  {
    id: "salon-2",
    title: "Forum Emploi Santé",
    description:
      "Salon spécialisé dans les métiers de la santé. Découvrez les opportunités dans les hôpitaux, cliniques, et entreprises pharmaceutiques.",
    organizer: "Santé Emploi",
    location: {
      address: "Centre de Congrès",
      city: "Lyon",
      postalCode: "69002",
      region: "Auvergne-Rhône-Alpes",
      country: "France",
    },
    startDate: new Date("2024-05-20"),
    endDate: new Date("2024-05-21"),
    registrationDeadline: new Date("2024-05-15"),
    targetAudience: ["Infirmiers", "Médecins", "Pharmaciens", "Techniciens"],
    sectors: ["Santé", "Pharmaceutique", "Recherche médicale"],
    expectedCompanies: 80,
    isVirtual: false,
    website: "https://forum-emploi-sante.fr",
    createdAt: new Date("2024-01-11"),
    updatedAt: new Date("2024-01-17"),
    isActive: true,
  },
];

// Données de démonstration pour les événements
export const mockEvenements: Evenement[] = [
  {
    id: "evenement-1",
    title: "Conférence IA & Futur du Travail",
    description:
      "Découvrez comment l'intelligence artificielle transforme le monde du travail. Conférences, ateliers et networking avec les experts du domaine.",
    type: "conference",
    organizer: "AI Future Institute",
    location: {
      address: "Palais des Congrès",
      city: "Paris",
      postalCode: "75017",
      region: "Île-de-France",
      country: "France",
    },
    startDate: new Date("2024-06-10"),
    endDate: new Date("2024-06-11"),
    registrationDeadline: new Date("2024-06-05"),
    targetAudience: ["Professionnels IT", "Étudiants", "Entrepreneurs"],
    topics: [
      "Intelligence Artificielle",
      "Machine Learning",
      "Automatisation",
      "Éthique IA",
    ],
    speakers: [
      {
        name: "Dr. Marie Dubois",
        title: "Directrice de Recherche",
        company: "INRIA",
        bio: "Experte en IA et éthique numérique",
      },
      {
        name: "Jean-Pierre Martin",
        title: "CTO",
        company: "TechGiant",
        bio: "Pionnier de l'IA en entreprise",
      },
    ],
    capacity: 500,
    isVirtual: false,
    isFree: false,
    price: 150,
    website: "https://ai-future-conference.com",
    registrationUrl: "https://ai-future-conference.com/register",
    createdAt: new Date("2024-01-07"),
    updatedAt: new Date("2024-01-23"),
    isActive: true,
  },
  {
    id: "evenement-2",
    title: "Atelier Entrepreneuriat Étudiant",
    description:
      "Atelier pratique pour apprendre les bases de l'entrepreneuriat. Créez votre business plan et rencontrez des mentors expérimentés.",
    type: "workshop",
    organizer: "Startup Campus",
    location: {
      address: "10 Rue de l'Innovation",
      city: "Toulouse",
      postalCode: "31000",
      region: "Occitanie",
      country: "France",
    },
    startDate: new Date("2024-03-25"),
    registrationDeadline: new Date("2024-03-20"),
    targetAudience: ["Étudiants", "Jeunes diplômés"],
    topics: ["Entrepreneuriat", "Business Plan", "Financement", "Pitch"],
    capacity: 50,
    isVirtual: false,
    isFree: true,
    website: "https://startup-campus.fr",
    createdAt: new Date("2024-01-13"),
    updatedAt: new Date("2024-01-20"),
    isActive: true,
  },
];

// Fonction pour obtenir tous les contenus
export function getAllContent() {
  return [...mockFormations, ...mockStages, ...mockSalons, ...mockEvenements];
}
