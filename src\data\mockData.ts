import { Formation, Stage, SalonEmploi, Evenement } from "@/types";

export const mockFormations: Formation[] = [
  {
    id: "formation-1",
    title: "Master en Intelligence Artificielle",
    description:
      "Formation complète en IA couvrant le machine learning, deep learning, et les applications pratiques.",
    type: "formation",
    formationType: "diplome",
    level: "bac+5",
    domain: "Informatique",
    institution: "École Polytechnique",
    location: {
      address: "91 Route de Saclay",
      city: "Palaiseau",
      postalCode: "91120",
      region: "Île-de-France",
      country: "France",
      coordinates: { lat: 48.7144, lng: 2.2147 },
    },
    duration: "2 ans",
    startDate: new Date("2024-09-01"),
    endDate: new Date("2026-06-30"),
    applicationDeadline: new Date("2024-05-15"),
    cost: 15000,
    isOnline: false,
    prerequisites: ["Licence en informatique", "Mathématiques niveau L3"],
    skills: [
      "Python",
      "TensorFlow",
      "Machine Learning",
      "Deep Learning",
      "Data Science",
    ],
    website: "https://www.polytechnique.edu",
    contactEmail: "<EMAIL>",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-20"),
    isActive: true,
  },
  // ... autres formations
];

export const mockStages: Stage[] = [
  {
    id: "stage-1",
    title: "Stage Développeur Frontend React",
    description:
      "Rejoignez notre équipe de développement pour créer des interfaces utilisateur modernes et performantes.",
    type: "stage",
    company: "TechCorp",
    position: "Développeur Frontend",
    domain: "Informatique",
    location: {
      address: "42 Avenue des Champs-Élysées",
      city: "Paris",
      postalCode: "75008",
      region: "Île-de-France",
      country: "France",
    },
    duration: "6 mois",
    startDate: new Date("2024-04-01"),
    endDate: new Date("2024-09-30"),
    applicationDeadline: new Date("2024-03-15"),
    salary: 1200,
    level: "bac+3",
    isRemote: false,
    skills: ["React", "TypeScript", "CSS", "Git", "Agile"],
    benefits: [
      "Tickets restaurant",
      "Transport remboursé",
      "Télétravail partiel",
    ],
    website: "https://techcorp.com",
    contactEmail: "<EMAIL>",
    applicationUrl: "https://techcorp.com/careers/stage-frontend",
    createdAt: new Date("2024-01-08"),
    updatedAt: new Date("2024-01-16"),
    isActive: true,
  },
  // ... autres stages
];

export const mockSalons: SalonEmploi[] = [
  {
    id: "salon-1",
    title: "Salon de l'Emploi Tech 2024",
    description: "Le plus grand salon dédié aux métiers du numérique.",
    type: "salon",
    organizer: "TechEvents France",
    domain: "Technologie",
    location: {
      address: "Parc des Expositions",
      city: "Paris",
      postalCode: "75015",
      region: "Île-de-France",
      country: "France",
    },
    startDate: new Date("2024-04-15"),
    endDate: new Date("2024-04-16"),
    registrationDeadline: new Date("2024-04-10"),
    targetAudience: [
      "Développeurs",
      "Data Scientists",
      "Product Managers",
      "Designers",
    ],
    sectors: ["Informatique", "Intelligence Artificielle", "Cybersécurité"],
    expectedCompanies: 150,
    isVirtual: false,
    registrationFee: 0,
    website: "https://salon-emploi-tech.fr",
    registrationUrl: "https://salon-emploi-tech.fr/inscription",
    createdAt: new Date("2024-01-05"),
    updatedAt: new Date("2024-01-22"),
    isActive: true,
  },
  // ... autres salons
];

export const mockEvenements: Evenement[] = [
  {
    id: "evenement-1",
    title: "Conférence IA & Futur du Travail",
    description:
      "Découvrez comment l'intelligence artificielle transforme le monde du travail.",
    type: "evenement",
    organizer: "AI Future Institute",
    domain: "Intelligence Artificielle",
    location: {
      address: "Palais des Congrès",
      city: "Paris",
      postalCode: "75017",
      region: "Île-de-France",
      country: "France",
    },
    startDate: new Date("2024-06-10"),
    endDate: new Date("2024-06-11"),
    registrationDeadline: new Date("2024-06-05"),
    targetAudience: ["Professionnels IT", "Étudiants", "Entrepreneurs"],
    topics: [
      "Intelligence Artificielle",
      "Machine Learning",
      "Automatisation",
      "Éthique IA",
    ],
    speakers: [
      {
        name: "Dr. Marie Dubois",
        title: "Directrice de Recherche",
        company: "INRIA",
        bio: "Experte en IA et éthique numérique",
      },
    ],
    capacity: 500,
    isVirtual: false,
    isFree: false,
    price: 150,
    website: "https://ai-future-conference.com",
    registrationUrl: "https://ai-future-conference.com/register",
    createdAt: new Date("2024-01-07"),
    updatedAt: new Date("2024-01-23"),
    isActive: true,
  },
  // ... autres événements
];

export function getAllContent() {
  return [...mockFormations, ...mockStages, ...mockSalons, ...mockEvenements];
}
