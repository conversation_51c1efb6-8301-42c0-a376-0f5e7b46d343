import { SITE_CONFIG } from "@/constants";
import { ContentItem, Evenement, Formation, SalonEmploi, Stage } from "@/types";
import { Metadata } from "next";

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: "website" | "article";
  publishedTime?: string;
  modifiedTime?: string;
}

export function generateMetadata({
  title,
  description = SITE_CONFIG.description,
  keywords = [],
  image = SITE_CONFIG.ogImage,
  url = SITE_CONFIG.url,
  type = "website",
  publishedTime,
  modifiedTime,
}: SEOProps = {}): Metadata {
  const fullTitle = title ? `${title} | ${SITE_CONFIG.name}` : SITE_CONFIG.name;
  const fullUrl = url.startsWith("http") ? url : `${SITE_CONFIG.url}${url}`;

  const defaultKeywords = [
    "formation",
    "stage",
    "emploi",
    "salon emploi",
    "événement",
    "étudiant",
    "alternant",
    "France",
    "carrière",
    "éducation",
  ];

  return {
    title: fullTitle,
    description,
    keywords: [...defaultKeywords, ...keywords],
    openGraph: {
      type,
      locale: "fr_FR",
      url: fullUrl,
      title: fullTitle,
      description,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title || SITE_CONFIG.name,
        },
      ],
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
    },
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description,
      images: [image],
      creator: "@educonnectfr",
    },
    alternates: {
      canonical: fullUrl,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
  };
}

export function generateContentMetadata(
  item: ContentItem,
  type: string
): Metadata {
  const title = item.title;
  const description =
    item.description.length > 160
      ? `${item.description.substring(0, 157)}...`
      : item.description;

  const keywords = [
    type,
    item.location.city,
    item.location.region,
    ...("domain" in item && item.domain ? [item.domain] : []),
    ...("skills" in item && item.skills ? item.skills.slice(0, 5) : []),
  ];

  return generateMetadata({
    title,
    description,
    keywords,
    url: `/${type}s/${item.id}`,
    type: "article",
    publishedTime: item.createdAt.toISOString(),
    modifiedTime: item.updatedAt.toISOString(),
  });
}

interface StructuredDataBase {
  "@context": string;
  "@type": string;
  name: string;
  description: string;
  url: string;
  dateCreated: string;
  dateModified: string;
  location: {
    "@type": string;
    name: string;
    address: {
      "@type": string;
      addressLocality: string;
      addressRegion: string;
      addressCountry: string;
      postalCode: string;
    };
  };
}

interface CourseStructuredData extends StructuredDataBase {
  "@type": "Course";
  provider: {
    "@type": "Organization";
    name: string;
  };
  educationalLevel: string;
  courseMode: string;
  offers?: {
    "@type": "Offer";
    price: number;
    priceCurrency: string;
  };
}

interface JobPostingStructuredData extends StructuredDataBase {
  "@type": "JobPosting";
  hiringOrganization: {
    "@type": "Organization";
    name: string;
  };
  jobLocation: StructuredDataBase["location"];
  employmentType: string;
  baseSalary?: {
    "@type": "MonetaryAmount";
    currency: string;
    value: {
      "@type": "QuantitativeValue";
      value: number;
      unitText: string;
    };
  };
}

interface EventStructuredData extends StructuredDataBase {
  "@type": "Event";
  startDate: string;
  endDate?: string;
  organizer: {
    "@type": "Organization";
    name: string;
  };
  eventStatus: string;
  eventAttendanceMode: string;
  offers?: {
    "@type": "Offer";
    price: number;
    priceCurrency: string;
  };
}

type StructuredData =
  | CourseStructuredData
  | JobPostingStructuredData
  | EventStructuredData
  | StructuredDataBase;

export function generateStructuredData(
  item: ContentItem,
  type: string
): StructuredData {
  const baseData: StructuredDataBase = {
    "@context": "https://schema.org",
    "@type": getSchemaType(type),
    name: item.title,
    description: item.description,
    url: `${SITE_CONFIG.url}/${type}s/${item.id}`,
    dateCreated: item.createdAt.toISOString(),
    dateModified: item.updatedAt.toISOString(),
    location: {
      "@type": "Place",
      name: item.location.city,
      address: {
        "@type": "PostalAddress",
        addressLocality: item.location.city,
        addressRegion: item.location.region,
        addressCountry: item.location.country,
        postalCode: item.location.postalCode,
      },
    },
  };

  // Données spécifiques selon le type
  switch (type) {
    case "formation": {
      const formation = item as Formation;
      return {
        ...baseData,
        "@type": "Course",
        provider: {
          "@type": "Organization",
          name: formation.institution,
        },
        educationalLevel: formation.level,
        courseMode: formation.isOnline ? "online" : "onsite",
        ...(formation.cost && {
          offers: {
            "@type": "Offer",
            price: formation.cost,
            priceCurrency: "EUR",
          },
        }),
      };
    }

    case "stage": {
      const stage = item as Stage;
      return {
        ...baseData,
        "@type": "JobPosting",
        hiringOrganization: {
          "@type": "Organization",
          name: stage.company,
        },
        jobLocation: baseData.location,
        employmentType: "INTERN",
        ...(stage.salary && {
          baseSalary: {
            "@type": "MonetaryAmount",
            currency: "EUR",
            value: {
              "@type": "QuantitativeValue",
              value: stage.salary,
              unitText: "MONTH",
            },
          },
        }),
      };
    }

    case "salon": {
      const salon = item as SalonEmploi;
      return {
        ...baseData,
        "@type": "Event",
        startDate: salon.startDate.toISOString(),
        endDate: salon.endDate?.toISOString(),
        organizer: {
          "@type": "Organization",
          name: salon.organizer,
        },
        eventStatus: "https://schema.org/EventScheduled",
        eventAttendanceMode: salon.isVirtual
          ? "https://schema.org/OnlineEventAttendanceMode"
          : "https://schema.org/OfflineEventAttendanceMode",
      };
    }

    case "evenement": {
      const evenement = item as Evenement;
      return {
        ...baseData,
        "@type": "Event",
        startDate: evenement.startDate.toISOString(),
        endDate: evenement.endDate?.toISOString(),
        organizer: {
          "@type": "Organization",
          name: evenement.organizer,
        },
        eventStatus: "https://schema.org/EventScheduled",
        eventAttendanceMode: evenement.isVirtual
          ? "https://schema.org/OnlineEventAttendanceMode"
          : "https://schema.org/OfflineEventAttendanceMode",
        ...(evenement.price && {
          offers: {
            "@type": "Offer",
            price: evenement.price,
            priceCurrency: "EUR",
          },
        }),
      };
    }

    default:
      return baseData;
  }
}

function getSchemaType(type: string): string {
  switch (type) {
    case "formation":
      return "Course";
    case "stage":
      return "JobPosting";
    case "salon":
    case "evenement":
      return "Event";
    default:
      return "Thing";
  }
}

export function generateBreadcrumbStructuredData(
  items: Array<{ name: string; url: string }>
) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url.startsWith("http")
        ? item.url
        : `${SITE_CONFIG.url}${item.url}`,
    })),
  };
}

export function generateOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    description: SITE_CONFIG.description,
    logo: `${SITE_CONFIG.url}/logo.png`,
    sameAs: [
      SITE_CONFIG.links.twitter,
      SITE_CONFIG.links.linkedin,
      SITE_CONFIG.links.github,
    ].filter(Boolean),
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "customer service",
      availableLanguage: "French",
    },
  };
}
