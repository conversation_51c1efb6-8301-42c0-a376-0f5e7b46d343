// types/index.ts

export interface BaseContent {
  id: string;
  title: string;
  description: string;
  type: ContentType;
  domain: string;
  location: Location;
  startDate: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  contactEmail?: string;
  website?: string;
}

export interface Formation extends BaseContent {
  type: "formation";
  institution: string;
  level: AcademicLevel;
  formationType: FormationType;
  duration: string;
  applicationDeadline?: Date;
  cost?: number;
  isOnline: boolean;
  prerequisites?: string[];
  skills: string[];
  contactPhone?: string;
}

export interface Stage extends BaseContent {
  type: "stage";
  company: string;
  position: string;
  duration: string;
  applicationDeadline?: Date;
  salary?: number;
  level: AcademicLevel;
  isRemote: boolean;
  skills: string[];
  benefits?: string[];
  applicationUrl?: string;
}

export interface SalonEmploi extends BaseContent {
  type: "salon";
  organizer: string;
  registrationDeadline?: Date;
  targetAudience: string[];
  sectors: string[];
  expectedCompanies: number;
  isVirtual: boolean;
  registrationFee?: number;
  registrationUrl?: string;
}

export interface Evenement extends BaseContent {
  type: "evenement";
  organizer: string;
  registrationDeadline?: Date;
  targetAudience: string[];
  topics: string[];
  speakers?: Speaker[];
  capacity?: number;
  isVirtual: boolean;
  isFree: boolean;
  price?: number;
  registrationUrl?: string;
}

export type AcademicLevel = "bac" | "bac+2" | "bac+3" | "bac+5" | "autre";
export type FormationType = "diplome" | "certification" | "formation";
export type EventType =
  | "conference"
  | "workshop"
  | "webinar"
  | "networking"
  | "autre";

export interface Location {
  address: string;
  city: string;
  postalCode: string;
  region: string;
  country: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface Speaker {
  name: string;
  title: string;
  company?: string;
  bio?: string;
  photo?: string;
}

export interface SearchFilters {
  query?: string;
  types?: ContentType[];
  location?: {
    city?: string;
    region?: string;
    radius?: number;
  };
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  domains?: string[];
  levels?: string[];
  isRemote?: boolean;
  isFree?: boolean;
  sortBy?: "date" | "relevance" | "location" | "title";
  sortOrder?: "asc" | "desc";
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export type ContentType = "formation" | "stage" | "salon" | "evenement";
export type Content = Formation | Stage | SalonEmploi | Evenement;

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface NavigationItem {
  label: string;
  href: string;
  icon?: string;
  children?: NavigationItem[];
}

export interface PlatformStats {
  totalFormations: number;
  totalStages: number;
  totalSalons: number;
  totalEvenements: number;
  activeUsers: number;
  lastUpdate: Date;
}
